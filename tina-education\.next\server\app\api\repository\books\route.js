/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/repository/books/route";
exports.ids = ["app/api/repository/books/route"];
exports.modules = {

/***/ "(rsc)/./app/api/repository/books/route.ts":
/*!*******************************************!*\
  !*** ./app/api/repository/books/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../prisma */ \"(rsc)/./prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get(\"search\") || \"\";\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const genreSlug = searchParams.get(\"genre\");\n        // Build where clause for books and ebooks\n        const where = {\n            type: {\n                in: [\n                    \"BOOK\",\n                    \"EBOOK\",\n                    \"AUDIOBOOK\"\n                ]\n            }\n        };\n        // Add genre filtering\n        if (genreSlug) {\n            where.genre = {\n                OR: [\n                    {\n                        slug: genreSlug\n                    },\n                    {\n                        parent: {\n                            slug: genreSlug\n                        }\n                    }\n                ]\n            };\n        }\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    abstract: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    keywords: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    user: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        // Calculate pagination\n        const skip = (page - 1) * limit;\n        // Fetch books with pagination\n        const [books, totalCount] = await Promise.all([\n            _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.publication.findMany({\n                where,\n                include: {\n                    user: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    genre: {\n                        include: {\n                            parent: true\n                        }\n                    }\n                },\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                skip,\n                take: limit\n            }),\n            _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.publication.count({\n                where\n            })\n        ]);\n        // Calculate pagination info\n        const totalPages = Math.ceil(totalCount / limit);\n        const hasNextPage = page < totalPages;\n        const hasPrevPage = page > 1;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            books: books.map((book)=>({\n                    ...book,\n                    createdAt: book.createdAt.toISOString(),\n                    updatedAt: book.updatedAt.toISOString()\n                })),\n            pagination: {\n                currentPage: page,\n                totalPages,\n                totalCount,\n                hasNextPage,\n                hasPrevPage,\n                limit\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to fetch books:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch books\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/repository/books/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frepository%2Fbooks%2Froute&page=%2Fapi%2Frepository%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frepository%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frepository%2Fbooks%2Froute&page=%2Fapi%2Frepository%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frepository%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_repository_books_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/repository/books/route.ts */ \"(rsc)/./app/api/repository/books/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/repository/books/route\",\n        pathname: \"/api/repository/books\",\n        filename: \"route\",\n        bundlePath: \"app/api/repository/books/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\repository\\\\books\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_repository_books_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZyZXBvc2l0b3J5JTJGYm9va3MlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnJlcG9zaXRvcnklMkZib29rcyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnJlcG9zaXRvcnklMkZib29rcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNnRjtBQUM3SjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGFwaVxcXFxyZXBvc2l0b3J5XFxcXGJvb2tzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9yZXBvc2l0b3J5L2Jvb2tzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcmVwb3NpdG9yeS9ib29rc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcmVwb3NpdG9yeS9ib29rcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGJpY2hlXFxcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcXFxSZXZpZXcgUmVxdWVzdCBiYWNrZW5kXFxcXEFSUlMtTmV4dEpTXFxcXHRpbmEtZWR1Y2F0aW9uXFxcXGFwcFxcXFxhcGlcXFxccmVwb3NpdG9yeVxcXFxib29rc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frepository%2Fbooks%2Froute&page=%2Fapi%2Frepository%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frepository%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frepository%2Fbooks%2Froute&page=%2Fapi%2Frepository%2Fbooks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frepository%2Fbooks%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();